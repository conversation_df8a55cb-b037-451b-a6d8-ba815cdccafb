import httpClient from "@clients/httpClient";
import { FormStatus } from "@enums/form-status";
import { IDefaultParams } from "@interface/common.interface";
import {
  IFormTransmittalPayload,
  ITransmittalLetterPayload,
  TFormTransmittalOutgoingPayload,
  TTransmittalFormTrailStatusPayload,
  TCreateReturnedPayload,
  TCancelReceipt,
  TIssueReceipt,
  TUpdateTransmitalStatus,
} from "@state/types/form-inventory-transmittal";

const apiResource = "form-transmittal";

export const getTransmittalService = async (formTransmittalId?: number) => {
  return httpClient.get(
    `${apiResource}/${formTransmittalId}?relations=padAssignments.form|createdBy.position|formTransmittalTrails.createdBy.position|formTransmittalTrails.releasedTo|formTransmittalTrails.releasedArea|padAssignments.createdBy.position|approval.signatories|latestFormTransmittalTrail.createdBy.position|latestFormTransmittalTrail.releasedTo.position|latestFormTransmittalTrail.releasedArea|releasedTo`
  );
};

export const getTransmittalsService = async (params: IDefaultParams & { exludeReturned?: boolean }) => {
  let query = `${apiResource}?pageSize=${params.pageSize ?? 100}&page=${params.page ?? 1}&relations=padAssignments.form|createdBy.position|approval.signatories`;

  if (params.exludeReturned) {
    query += `&status[neq]=${FormStatus.RETURNED}`;
  }

  if (params.filter) {
    query += `&padAssignments.form.atpNumber[like]=${params.filter}`;
  }

  if (params.type) {
    query += `&padAssignments.form.formTypeId[eq]=${params.type}`;
  }

  if (params.divisionFilter) {
    query += `&padAssignments.form.divisionId[eq]=${params.divisionFilter}`;
  }
  if (params.areaFilter) {
    query += `&releasedArea[eq]=${params.areaFilter}`;
  }
  //Filter by date
  if (params?.dateFrom && params?.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }
  if (params.statusFilter) {
    query += `&status[eq]=${params.statusFilter}`;
  }

  query += `&sort=id,desc`;

  return httpClient.get(query);
};

export const getFormTransmittalActivityLogsService = async (formTransmittalId: number) => {
  return httpClient.get(`${apiResource}/${formTransmittalId}/activity-logs?relations=causer.position`);
};

export const postFormTransmittalService = async (payload: IFormTransmittalPayload) => {
  return httpClient.post(`${apiResource}`, payload);
};

//Form Transmittal Trail
const apiTrailResource = "form-transmittal-trail";

export const getTransmittalTrailService = async (formTransmittalId?: number) => {
  return httpClient.get(
    // `${apiResource}/${formTransmittalId}?relations=padAssignments.form.attachments|createdBy.position|approval.signatories|formTransmittalTrails|latestFormTransmittalTrail|latestFormTransmittalTrail.createdBy|latestFormTransmittalTrail.releasedTo`
    `${apiResource}/${formTransmittalId}?relations=padAssignments.form.attachments|createdBy.position|approval.signatories|formTransmittalTrails|latestFormTransmittalTrail|latestFormTransmittalTrail.createdBy|latestFormTransmittalTrail.releasedTo|latestFormTransmittalTrail.releasedArea|releasedTo|releasedArea`
  );
};

export const getCurrentUserFormTransmittalTrailService = async (params: IDefaultParams & { useReturnedPads?: boolean }) => {
  // Determine the relation path based on the useReturnedPads flag
  const formRelationPath = params.useReturnedPads ? "returnedPads.form" : "padAssignments.form";

  // Build the base query with the conditional relation
  let query = `${apiResource}/trail/me?relations=${formRelationPath}|createdBy.position|formTransmittalTrails.createdBy.position|formTransmittalTrails.releasedTo.position|formTransmittalTrails.releasedArea&sort=updatedAt,desc`;

  if (params.page) {
    query += `&page=${params.page}`;
  }
  if (params.pageSize) {
    query += `&pageSize=${params.pageSize}`;
  }

  if (params?.dateFrom && params?.dateTo) {
    query += `&formTransmittalTrails.createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }

  if (params.parentFilter) {
    query += `&formTransmittalTrails.status[eq]=${params.parentFilter}`;
  }

  if (params.statusFilter) {
    query += `&status[eq]=${params.statusFilter}`;
  }

  if (params.id) {
    query += `&formTransmittalTrails.createdBy.id[eq]=${params.id}`;
  }


  // Update the division filter to use the correct relation path
  if (params.divisionFilter) {
    const divisionRelationPath = params.useReturnedPads ? "returnedPads.form.divisionId" : "padAssignments.form.divisionId";

    query += `&${divisionRelationPath}[eq]=${params.divisionFilter}`;
  }

  return httpClient.get(query);
};

export const getTransmittalsTrailService = async (params: IDefaultParams) => {
  let query = `${apiResource}?relations=padAssignments.form|createdBy.position|latestFormTransmittalTrail.createdBy.position|latestFormTransmittalTrail.releasedTo|latestFormTransmittalTrail.releasedArea&sort=id,desc`;
  // padAssignments.form|createdBy.position|latestFormTransmittalTrail.createdBy.position|latestFormTransmittalTrail.releasedTo.position|latestFormTransmittalTrail.releasedArea
  if (params.page) {
    query += `&page=${params.page}`;
  }
  if (params.pageSize) {
    query += `&pageSize=${params.pageSize}`;
  }

  if (params.type) {
    query += `&latestFormTransmittalTrail.createdBy.position.positionName[eq]=${params.type}`;
  }

  if (params.statusFilter) {
    query += `&latestFormTransmittalTrail.status[eq]=${params.statusFilter}`;
  }

  if (params.divisionFilter) {
    query += `&padAssignments.form.divisionId[eq]=${params.divisionFilter}`;
  }

  if (params.areaFilter) {
    query += `&releasedArea[eq]=${params.areaFilter}`;
  }

  if (params.id) {
    query += `&latestFormTransmittalTrail.releasedTo.id[eq]=${params.id}`;
  }

  // Filter by date
  if (params?.dateFrom && params?.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }

  query += `&pageSize=${params.pageSize ?? 100}&page=${params.page ?? 1}&sort=id,desc`;

  return httpClient.get(query);
};

export const getReturnedPadsService = async (params: IDefaultParams & { useNotEqual?: boolean }) => {
  let query = `${apiResource}?relations=returnedPads.form|returnedPads.padSeriesDetails|createdBy.position|latestFormTransmittalTrail.createdBy.position|latestFormTransmittalTrail.releasedTo|latestFormTransmittalTrail.releasedArea`;

  if (params.page) {
    query += `&page=${params.page}`;
  }
  if (params.pageSize) {
    query += `&pageSize=${params.pageSize}`;
  }

  if (params.type) {
    query += `&latestFormTransmittalTrail.createdBy.position.positionName[eq]=${params.type}`;
  }

  if (params.user) {
    query += `&latestFormTransmittalTrail.releasedTo[eq]=${params.user}`;
  }

  if (params.statusFilter) {
    query += `&latestFormTransmittalTrail.status[in]=${params.statusFilter}`;
  }

  if (params.statusFilter) {
    query += `&latestFormTransmittalTrail.status[in]=${params.statusFilter}`;
  }

  if (params.divisionFilter) {
    query += `&padAssignments.form.divisionId[eq]=${params.divisionFilter}`;
  }

  if (params.areaFilter) {
    query += `&releasedArea[eq]=${params.areaFilter}`;
  }

  if (params.sort) {
    query += `&releasedArea[eq]=${params.areaFilter}`;
  }

  // Filter by date
  if (params?.dateFrom && params?.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }

  query += `&pageSize=${params.pageSize ?? 100}&page=${params.page ?? 1}&sort=id,desc`;

  return httpClient.get(query);
};

export const getReturnedTransmittalTrailService = async (formTransmittalId?: number) => {
  return httpClient.get(
    `${apiResource}/${formTransmittalId}?relations=returnedPads.form.attachments|returnedPads.padSeriesDetails|createdBy.position|approval.signatories|formTransmittalTrails|formTransmittalTrails.releasedTo.position|formTransmittalTrails.releasedMethod|latestFormTransmittalTrail|latestFormTransmittalTrail.createdBy|latestFormTransmittalTrail.releasedTo|latestFormTransmittalTrail.releasedArea|releasedTo|releasedArea`
  );
};


// FUTURE USE
// export const getReturnedPadsDetailsService = async (formTransmittalId?: number) => {
//   let api = "pad-assignment/assigned-to-me";
//   let relations =
//     "relations=padSeriesDetails.cooperative|padSeriesDetails.product|formTransmittal|form.division|form.formType|form.area|formTransmittal|createdBy|padSeriesDetails.attachments&status[eq]=COMPLETED";

//   return httpClient.get(`${apiResource}/${formTransmittalId}?relations=returnedPads.form|returnedPads.padSeriesDetails`);
// }

export const postFormTransmittalTrailService = async (formTransmittalId: number, payload: TFormTransmittalOutgoingPayload) => {
  return httpClient.post(`${apiResource}/${formTransmittalId}/trail`, payload);
};

export const putFormTransmittalTrailService = async (formTransmittalTrailId: number, payload: TTransmittalFormTrailStatusPayload) => {
  return httpClient.put(`${apiTrailResource}/${formTransmittalTrailId}`, payload);
};

//Get transmittal trail
export const getLatestTransmittalTrailService = async (params: IDefaultParams) => {
  let query = `${apiResource}?relations=padAssignments.form|createdBy.position|formTransmittalTrails.createdBy.position|formTransmittalTrails.releasedTo.position|formTransmittalTrails.releasedArea&sort=id,desc`;
  if (params.page) {
    query += `&page=${params.page}`;
  }
  if (params.pageSize) {
    query += `&pageSize=${params.pageSize}`;
  }

  if (params.type) {
    query += `&latestFormTransmittalTrail.createdBy.position.positionName[eq]=${params.type}`;
  }

  if (params.statusFilter) {
    query += `&latestFormTransmittalTrail.status[eq]=${params.statusFilter}`;
  }

  if (params.divisionFilter) {
    query += `&padAssignments.form.divisionId[eq]=${params.divisionFilter}`;
  }
  
  if (params.user) {
    query += `&latestFormTransmittalTrail.releasedTo[eq]=${params.user}`;
  }

  return httpClient.get(query);
};

export const postFormTransmittalExportLetterOutgoingCashierService = async (payload: ITransmittalLetterPayload) => {
  return httpClient.post(`${apiResource}/export-letter/outgoing-cashier`, payload, {
    responseType: "blob",
  });
};
export const getTransmittalTrailActivityLogService = async (formTransmittalId: number) => {
  return httpClient.get(`form-transmittal/${formTransmittalId}/activity-logs?relations=causer.position`);
};
export const getTransmittalFormToClifsaPrintService = async (formTransmittalId?: number | string) => {
  return httpClient.get(`${apiResource}/${formTransmittalId}/export/clifsa`, {
    responseType: "blob", // Ensures the response is treated as a blob
  });
};

export const getCurrentUserOldestFormTransmittalTrailService = async (params: IDefaultParams & { eq?: boolean }) => {
  // Determine the relation path based on the useReturnedPads flag
  const formRelationPath = params.eq ? "returnedPads.form" : "padAssignments.form";

  let query = `${apiResource}/trail/me?relations=${formRelationPath}|createdBy.position|oldestFormTransmittalTrail.createdBy.position|oldestFormTransmittalTrail.releasedTo|oldestFormTransmittalTrail.releasedArea`;

  if (params.page) {
    query += `&page=1`;
  }
  if (params.pageSize) {
    query += `&pageSize=10`;
  }

  if (params?.dateFrom && params?.dateTo) {
    query += `&formTransmittalTrails.createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }

  if (params.statusFilter) {
    query += `&status[eq]=${params.statusFilter}`;
  }

  if (params.id) {
    query += `&formTransmittalTrails.createdBy.id[eq]=${params.id}`;
  }

  if (params.divisionFilter) {
    const divisionRelationPath = params.eq ? "returnedPads.form.divisionId" : "padAssignments.form.divisionId";

    query += `&${divisionRelationPath}[eq]=${params.divisionFilter}`;
  }

  return httpClient.get(query);
};

export const postReturnedPadsService = async (payload: TCreateReturnedPayload) => {
  const api = "form-transmittal";
  return httpClient.post(`${api}`, payload);
};

// Get pads assignments
const padApiResource = "pad-assignment";
export const getPadsAssignmentsService = async (params: IDefaultParams) => {
  let query = `${padApiResource}?relations=padSeriesDetails|formTransmittal|padSeriesDetails.cooperative|padSeriesDetails.product|padSeriesDetails.userRemitTo|padSeriesDetails.userIssuedBy`;

  if (params.page) {
    query += `&page=${params.page}`;
  }
  if (params.pageSize) {
    query += `&pageSize=${params.pageSize}`;
  }
  if (params.statusFilter) {
    query += `&status[eq]=${params.statusFilter}`;
  }
  if (params.divisionFilter) {
    query += `&divisionId[eq]=${params.divisionFilter}`;
  }
  if (params.areaFilter) {
    query += `&areaId[eq]=${params.areaFilter}`;
  }
  if (params?.dateFrom && params?.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }

  return httpClient.get(query);
};

export const getPadsAssignmentsByIdService = async (id: number) => {
  return httpClient.get(`${padApiResource}/${id}?relations=padSeriesDetails|formTransmittal`);
};

export const getUserPadAssignmentService = async (params: IDefaultParams) => {
  const api = "pad-assignment/assigned-to-me";

  let query = `${api}?relations=padSeriesDetails|formTransmittal|form.division|formTransmittal&status[eq]=ACTIVE`;

  if (params.page) {
    query += `&page=${params.page}`;
  }

  if (params.pageSize) {
    query += `&pageSize=${params.pageSize}`;
  }

  if (params.divisionFilter) {
    query += `&form.divisionId[eq]=${params.divisionFilter}`;
  }

  if (params.formtypeFilter) {
    query += `&form.formTypeId[eq]=${params.formtypeFilter}`;
  }

  return httpClient.get(query);
};

export const getUserPadAssignmentByIdService = async (params: IDefaultParams) => {
  const api = "pad-assignment";
  const relations = "relations=padSeriesDetails.cooperative|padSeriesDetails.product|padSeriesDetails.paymentDetail|formTransmittal|form.division|formTransmittal&status[eq]=ACTIVE";

  let query = `${api}/${params.id}?${relations}`;

  query += `&padSeriesDetails.cooperative.coopName[like]=north`;

  // search filter
  if (params?.filter) {
    query += `&padSeriesDetails.cooperative.coopName[like]=${params?.statusFilter}`;
  }

  // Filter by Date
  if (params?.dateFrom && params?.dateTo) {
    query += `&createdAt[between]=${params.dateFrom},${params.dateTo}`;
  }

  return httpClient.get(`${query}`);
};

export const getUserPadSeriesDetailsService = async (id: number) => {
  const api = "pad-series";
  const relations =
    "relations=cooperative|product|userRemitTo|userIssuedBy|userRemitTo|paymentDetail.paymentMethod|padAssignment.form.division|padAssignment.form.area|padAssignment.form.formType|padAssignment.formTransmittal.latestFormTransmittalTrail|paymentDetail.bankAccount.bank|attachments";

  let query = `${api}/${id}`;

  return httpClient.get(`${query}?${relations}`);
};

export const putIssueReceiptService = async (payload: TIssueReceipt) => {
  const formData = new FormData();
  const api = "pad-series";
  const { id, paymentDetail, ...updateData } = payload;

  // main data
  Object.keys(updateData).forEach((key) => {
    if (key === "attachments" && Array.isArray(payload.attachments)) {
      payload.attachments.forEach((attachment, index) => {
        if (attachment.file) {
          formData.append(`attachments[${index}][file]`, attachment.file);
        }
        if (attachment.label !== undefined) {
          formData.append(`attachments[${index}][label]`, attachment.label);
        }
        if (attachment.description !== undefined) {
          formData.append(`attachments[${index}][description]`, attachment.description);
        }
      });
    } else {
      formData.append(key, (updateData as any)[key]);
    }
  });

  // payment details
  Object.keys(paymentDetail).forEach((key) => {
    formData.append(`paymentDetail[${key}]`, (paymentDetail as any)[key]);
  });

  // based on postman example. Having error in using PUT method.
  formData.append("_method", "PUT");

  return httpClient.post(`${api}/${id}`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

export const putCancelReceiptService = async (payload: TCancelReceipt) => {
  const formData = new FormData();
  const api = "pad-series";
  const { id, ...updateData } = payload;

  Object.keys(updateData).forEach((key) => {
    if (key === "attachments" && Array.isArray(payload.attachments)) {
      payload.attachments.forEach((attachment, index) => {
        if (attachment.file) {
          formData.append(`attachments[${index}][file]`, attachment.file);
        }
        if (attachment.label !== undefined) {
          formData.append(`attachments[${index}][label]`, attachment.label);
        }
        if (attachment.description !== undefined) {
          formData.append(`attachments[${index}][description]`, attachment.description);
        }
      });
    } else {
      formData.append(key, (updateData as any)[key]);
    }
  });

  // based on postman example. Having error in using PUT method.
  formData.append("_method", "PUT");

  return httpClient.post(`${api}/${id}/cancel`, formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

export const putTransmittalTrailService = async (payload: TUpdateTransmitalStatus) => {
  const api = "form-transmittal-trail";
  const { id, ...updatePayload } = payload;

  return httpClient.put(`${api}/${id}`, updatePayload);
};
