import { FC, useEffect, useState } from "react";
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import { FaCircleCheck } from "react-icons/fa6";
import { FaCircleXmark } from "react-icons/fa6";
import TextField from "@components/form/TextField";
import FileDropzone from "@components/common/FileDropzone";
import { FaCloudArrowUp } from "react-icons/fa6";
import Typography from "@components/common/Typography";
import TextArea from "@components/form/TextArea";
import CheckBox from "@components/form/CheckBox";
import { Form, FormikProvider, useFormik } from "formik";
import dayjs from "dayjs";
import { postProductProposalApproval } from "@services/proposal/proposal.service";
import { showSuccess } from "@helpers/prompt";
import { AxiosResponse } from "axios";
import { ApprovalRejectSchema, ApprovalSchema } from "@services/product-proposal/product-proposal.schema";
import { ProposalApprovalStatus } from "@enums/proposal-status";
import DisplayAERDetails from "./AERDetails";
import httpClient from "@clients/httpClient";
import { toast } from "react-toastify";
import { PiPrinter } from "react-icons/pi";
import Select1 from "@components/form/Combo-box1";

import { ICommissionDistributions } from "@interface/quotation.interface";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { useSelector } from "react-redux";
import { RootState } from "@state/reducer";
import { canCreateProductProposal } from "@helpers/product-proposal/product-proposal-permissions";
import { IUserRPermission } from "@interface/user.interface";
interface ProductProposalProps {
  data: any;
}

const ProductProposalAER: FC<ProductProposalProps> = ({ data }) => {
  const [updateStatusModal, setUpdateStatusModal] = useState<boolean>();
  const [updateStatus, setUpdateStatus] = useState<string>("");
  const cooperativeOfficers = data?.cooperative?.cooperativeOfficers;
  const [viewCoopDetailsModal, setCoopDetailsModal] = useState<boolean>();
  const { putProductProposalCommission } = useProductProposalActions();

  //check perm
  const currentUser = useSelector((state: RootState) => state.auth.user.data);
  const user = { ...currentUser, roles: currentUser?.roles ?? [] } as IUserRPermission;
  const canCreateProposal = canCreateProductProposal(user);

  const getManagementFee = (opt: number) =>
    data?.proposable?.quotation?.quotationCommissionDistribution
      .filter((rate: ICommissionDistributions) => Number(rate.option) === Number(opt) && Number(rate.commissionTypeId) === 1) //#1 -> id of management fee
      ?.map((item: ICommissionDistributions) => item.rate);
  const formik2 = useFormik({
    initialValues: data
      ? {
          proposalId: data.id,
          managementPercentFee: null,
        }
      : {},
    enableReinitialize: true,
    onSubmit: async (values) => {
      try {
        if (!values?.managementPercentFee) {
          return;
        }
        putProductProposalCommission(values as any);
      } catch (error) {
        toast.error("Failed to update product proposal management fee.");
      }
    },
  });
  const parseOptionsData = (optionsString: string) => {
    try {
      if (!optionsString) return [];

      const parsed = JSON.parse(optionsString);

      // Handle different data structures
      if (Array.isArray(parsed)) {
        return parsed.map((opt, index) => {
          // If it's already an object with label/value
          if (typeof opt === "object" && opt !== null && ("label" in opt || "value" in opt)) {
            return {
              label: opt.label || `Option ${opt.value || index + 1}`,
              value: opt.value || opt.label || index + 1,
              text: opt.label || `Option ${opt.value || index + 1}`, // Add 'text' property
            };
          }
          // If it's a primitive value (number, string)
          return {
            label: `Option ${opt}`,
            value: opt,
            text: `Option ${opt}`, // Add 'text' property
          };
        });
      }

      // If it's an object, convert to array
      if (typeof parsed === "object" && parsed !== null) {
        return Object.entries(parsed).map(([_key, value]) => ({
          label: `Option ${value}`,
          value: value,
          text: `Option ${value}`, // Add 'text' property
        }));
      }

      return [];
    } catch (error) {
      console.error("Error parsing options:", error);
      return [];
    }
  };

  const formik = useFormik({
    initialValues: data?.proposalApproval ?? {
      approveRejectDate: "",
      approveRejectRemarks: "",
      selectedOption: "",
      hasPricingRemarks: false,
      hasUnderwritingRemarks: false,
      status: "",
      attachments: [],
    },
    validationSchema: updateStatus === "Approved" ? ApprovalSchema : ApprovalRejectSchema,
    onSubmit: async (values) => {
      const { createdAt, updatedAt, ...updatedData } = values;

      if (typeof updatedData.hasPricingRemarks === "boolean") {
        updatedData.hasPricingRemarks = updatedData.hasPricingRemarks ? 1 : 0;
      }

      if (typeof updatedData.hasUnderwritingRemarks === "boolean") {
        updatedData.hasUnderwritingRemarks = updatedData.hasUnderwritingRemarks ? 1 : 0;
      }

      try {
        const status: AxiosResponse = await postProductProposalApproval(data?.id as number, updatedData);

        if (status) {
          handleUpdateStatusModal();
          showSuccess("Success", "Product Proposal Approval has been updated!").then((result) => {
            if (result.isConfirmed) {
              window.location.reload();
              formik2.submitForm();
            }
          });
        }
      } catch (error) {
        console.error(error);
      }
    },
  });

  const handlePrint = async (apiUrl: string, queryParams?: string) => {
    try {
      const endpoint = queryParams ? `${apiUrl}?${queryParams}` : apiUrl;
      const response: any = await httpClient.get(endpoint, {
        responseType: "blob",
      });

      const blob = new Blob([response], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      window.open(url, "_blank");
    } catch (error) {
      toast.error(`PDF export failed: ${String(error)}`);
    }
  };

  const handleUpdateStatusModal = () => {
    setUpdateStatusModal((prev) => !prev);
    if (updateStatus === "Approved") {
      formik.setFieldValue("status", ProposalApprovalStatus.approved);
    } else if (updateStatus === "Reject") {
      formik.setFieldValue("status", ProposalApprovalStatus.rejected);
    }
  };

  const handleCoopDetailsModal = () => {
    setCoopDetailsModal((prev) => !prev);
  };

  const [files, setFiles] = useState<Array<File>>([]);
  const [imagePreview, setImagePreview] = useState<string | ArrayBuffer | null>(null);

  const handleFile = (acceptedFiles: Array<File>) => {
    setFiles(acceptedFiles);

    const file = new FileReader();
    file.onload = () => {
      setImagePreview(file.result);
    };

    file.readAsDataURL(acceptedFiles[0]);
  };

  useEffect(() => {
    if (files) {
      const fileArray = Array.from(files).map((file) => ({
        file: file,
        label: file.name,
        description: "Description here",
      }));

      formik.setFieldValue("attachments", fileArray);
    }
  }, [files]);
  return (
    <div>
      {viewCoopDetailsModal && (
        <Modal isOpen={viewCoopDetailsModal} onClose={handleCoopDetailsModal} modalContainerClassName="max-w-5xl  ">
          <div className="w-full h-[48rem] overflow-y-auto ">
            <div className="h-max  flex flex-col gap-4 pt-4">
              <div className="mb-2 text-xl font-semibold">BASIC INFORMATION</div>
              <div className="text-zinc-500">Cooperative Name (In Full)</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.coopName}</div>
              <div className="text-zinc-500">Cooperative Acronym</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.coopAcronym}</div>
              <div className="text-zinc-500">Cooperative Category</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.cooperativeCategory?.coopCategoryName}</div>
              <div className="text-zinc-500">Cooperative Type</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.cooperativeType?.coopTypeName}</div>
              <div className="text-zinc-500">No. of Branches</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.coopBranchesCount}</div>
              <div className="mb-2 text-xl font-semibold border-t-2 pt-8 mt-8 border-zinc-400"></div>
              <div className="text-zinc-500">Province</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.province}</div>
              <div className="text-zinc-500">Municipality/City</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.city}</div>
              <div className="text-zinc-500">Baranggay</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.barangay}</div>
              <div className="text-zinc-500">Street Address</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.streetAddress}</div>
              <div className="text-zinc-500">Zipcode</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.zipCode}</div>
              <div className="mb-2 text-xl font-semibold border-t-2 pt-8 mt-8 border-zinc-400">CONTACT INFORMATION</div>
              <div className="text-zinc-500">Email Address</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.emailAddress}</div>
              <div className="text-zinc-500">Website</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.websiteAddress}</div>
              <div className="text-zinc-500">Telephone No. Fax No.</div>
              <div className=" w-3/4 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.telephoneNumber}</div>
              <div className="mb-2 text-xl font-semibold border-t-2 pt-8 mt-8 border-zinc-400">REGISTRATION AND COMPLIANCE</div>
              <div className="flex mb-4">
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">CDA Reg No.</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.cdaRegistrationNumber}</div>
                </div>
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">Date Registered</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">
                    {data?.cooperative?.cdaRegistrationDate !== null && <span>{dayjs(data?.cooperative?.cdaRegistrationDate).format("MMMM DD, YYYY")}</span>}
                  </div>
                </div>
              </div>
              <div className="flex ">
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">CDA Certificate of Compliance No. (COC No.)</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.cdaCocNumber}</div>
                </div>
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">Date Registered</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">
                    {data?.cooperative?.cdaCocDate !== null && <span>{dayjs(data?.cooperative?.cdaCocDate).format("MMMM DD, YYYY")}</span>}
                  </div>
                </div>
              </div>
              <div className="mb-2 text-xl font-semibold border-t-2 pt-8 mt-8 border-zinc-400">TAX INFORMATION</div>
              <div className="flex mb-4">
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">Tax Identification No. (TIN)</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.taxIdNumber}</div>
                </div>
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">Certificate of Tax Exemption (CTE) No.</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">{data?.cooperative?.taxCteNumber}</div>
                </div>
              </div>
              <div className="flex mb-4">
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">Tax Identification No. Issue Date</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">
                    <span>{dayjs(data?.cooperative?.taxIdDate).format("MMMM DD, YYYY") ?? ""}</span>
                  </div>
                </div>
                <div className="w-1/2 flex flex-col gap-4">
                  {" "}
                  <div className="text-zinc-500">Certificate of Tax Exemption Expiration Date</div>
                  <div className=" w-5/6 pb-2 border-b border-zinc-300 font-semibold">
                    <span>{dayjs(data?.cooperative?.taxCteExpiryDate).format("MMMM DD, YYYY") ?? ""}</span>
                  </div>
                </div>
              </div>
              <div className="mb-2 text-xl font-semibold border-t-2 pt-8 mt-8 border-zinc-400">AFFILIATIONS</div>

              <div className="flex flex-col mb-4 w-full">
                <table className="table">
                  <thead>
                    <tr>
                      <th>AFFILIATIONS</th>
                      <th>STATUS</th>
                      <th>EFFECTIVE DATE</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data?.cooperative?.cooperativeAffiliations?.map((affiliation: any, index: number) => (
                      <tr key={index} className="border-b border-zinc-300">
                        <td>{affiliation?.affiliation?.affiliationName ?? "N/A"}</td>
                        <td>{affiliation?.status ?? "N/A"}</td>
                        <td>{affiliation?.effectivityDate ? dayjs(affiliation.effectivityDate).format("MMMM DD, YYYY") : "N/A"}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="mb-2 text-xl font-semibold border-t-2 pt-8 mt-8 border-zinc-400">COOP OFFICERS</div>

              <div className="flex flex-col mb-4 w-full">
                <table className="table">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Position</th>
                      <th>Title</th>
                      <th>Gender</th>
                      <th>Contact No.</th>
                      <th>Email Address</th>
                      <th>Status</th>
                      <th>Effective Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {cooperativeOfficers?.map((officer: any, index: number) => (
                      <tr key={index} className="border-b border-zinc-300">
                        <td>
                          {officer?.firstName} {officer?.lastName}
                        </td>
                        <td>{officer?.position?.positionName ?? "N/A"}</td>
                        <td>{officer?.title ?? "N/A"}</td>
                        <td>{officer?.gender ?? "N/A"}</td>
                        <td>{officer?.contactNumber ?? "N/A"}</td>
                        <td>{officer?.emailAddress ?? "N/A"}</td>
                        <td>{officer?.status ?? "N/A"}</td>
                        <td>{officer?.effectivityDate ? dayjs(officer.effectivityDate).format("MMMM DD, YYYY") : "N/A"}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </Modal>
      )}

      {updateStatusModal && (
        <Modal isOpen={updateStatusModal} onClose={handleUpdateStatusModal} modalContainerClassName="max-w-3xl ">
          <FormikProvider value={formik}>
            <Form onSubmit={formik.handleSubmit}>
              <div className="w-full flex flex-col  ">
                <div className=" flex items-center justify-center text-center mb-10">
                  {" "}
                  {updateStatus === "Approved" && <FaCircleCheck size={50} className="text-success" />}
                  {updateStatus === "Reject" && <FaCircleXmark size={50} className="text-danger" />}
                </div>

                <div className="text-3xl text-center font-poppins-semibold mb-10"> {updateStatus} Proposal</div>

                <div>
                  <label className="text-zinc-400">Please enter the date it wasit {updateStatus === "Approved" ? "approved" : "rejected"}.</label>
                  <TextField
                    name="approveRejectDate"
                    type="date"
                    value={formik.values.approveRejectDate}
                    error={formik.touched.approveRejectDate && !!formik.errors.approveRejectDate}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                  {formik.touched.approveRejectDate && formik.errors.approveRejectDate ? <div className="text-red-500 mb-4 text-sm">{formik.errors.approveRejectDate as string}</div> : null}
                </div>
                {updateStatus === "Approved" && (
                  <div className="mt-3">
                    <label className="text-zinc-400">Please select rate/optioncc.</label>
                    <Select1
                      name="selectedOption"
                      placeholder="Select an option..."
                      options={parseOptionsData(data?.proposable?.options || "[]")}
                      value={formik.values.selectedOption}
                      error={formik.touched.selectedOption && !!formik.errors.selectedOption}
                      onChange={(e) => {
                        formik2.setFieldValue("managementPercentFee", Number(getManagementFee(Number(e.target.value))[0]));
                        formik.handleChange(e);
                      }}
                      onBlur={formik.handleBlur}
                    />
                    {formik.touched.selectedOption && formik.errors.selectedOption ? <div className="text-red-500 mb-4 text-sm">{formik.errors.selectedOption as string}</div> : null}
                  </div>
                )}
                {updateStatus === "Approved" && (
                  <div className="mt-4">
                    <label>Upload Signed Document</label>
                    <div className="border border-zinc-400 rounded-xl mt-2 w-full h-60 flex items-center justify-center">
                      <FileDropzone setFiles={handleFile} height={200}>
                        {files.length === 0 && (
                          <div className="flex flex-1 flex-col items-center">
                            <FaCloudArrowUp size={30} className="mb-4" />
                            <Typography>Click or drag and drop to upload your profile</Typography>
                            <Typography className="text-slate-400">PNG, JPG (Max 20MB)</Typography>
                          </div>
                        )}
                        {imagePreview && (
                          <div className="flex flex-1 flex-col items-center  p-4 mb-4 rounded-md h-full w-full  ">
                            <img src={imagePreview as string} alt="Image Preview" className="w-full h-full object-contain" />
                          </div>
                        )}
                      </FileDropzone>
                    </div>
                  </div>
                )}

                {updateStatus === "Reject" && (
                  <>
                    <div className="mt-4 text-xl w-full text-center font-poppins-semibold">Remarks</div>{" "}
                    <div className="flex items-center  gap-4">
                      {" "}
                      <CheckBox name="hasPricingRemarks" checked={formik.values.hasPricingRemarks} onChange={formik.handleChange} /> Pricing
                    </div>
                    <div className="flex items-center  gap-4 mb-4">
                      {" "}
                      <CheckBox name="hasUnderwritingRemarks" checked={formik.values.hasUnderwritingRemarks} onChange={formik.handleChange} /> Underwriting Provisions
                    </div>
                    <TextArea
                      name="approveRejectRemarks"
                      placeholder="Enter Remarks"
                      value={formik.values.approveRejectRemarks}
                      error={formik.touched.approveRejectRemarks && !!formik.errors.approveRejectRemarks}
                      errorText={formik.errors.approveRejectRemarks as string}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    />
                  </>
                )}

                <div className="flex items-center justify-center gap-4 w-full mt-4">
                  <Button classNames="w-40 rounded-lg bg-zinc-500" onClick={handleUpdateStatusModal}>
                    Cancel
                  </Button>
                  <Button type="submit" classNames={`w-40 rounded-lg  ${updateStatus === "Approved" ? "bg-success" : "bg-danger"}`}>
                    {updateStatus === "Approved" ? "Approved" : "Reject"}
                  </Button>
                </div>
              </div>
            </Form>
          </FormikProvider>
        </Modal>
      )}

      <div className="p-4">
        {" "}
        <div className="w-full flex flex-col gap-4 mb-4">
          <div className="flex items-center justify-center">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Product Proposed</div>
              <div className="w-2/3 text-black"> {data?.product?.name ?? ""}</div>
            </div>
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400"> Creation Date</div>
              <div className="w-2/3 text-black">{data?.createdAt !== null && <span>{dayjs(data?.createdAt).format("MMMM DD, YYYY - h:m A")}</span>}</div>
            </div>
          </div>
          <div className="flex items-center justify-start">
            {" "}
            <div className="w-1/2 flex items-center justify-center">
              <div className="w-1/3 text-zinc-400">Cooperative</div>
              <div className="w-2/3 text-black">{data?.cooperative?.coopName}</div>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-between mb-2">
        <div>
          {" "}
          <Button type="button" variant="primary" classNames="text-xs" onClick={handleCoopDetailsModal}>
            View Coop Details
          </Button>
        </div>
        <div className="flex gap-2">
          {" "}
          {canCreateProposal && (
            <Button
              variant="primary"
              classNames="w-full md:w-auto flex gap-2"
              onClick={() => {
                const proposalId = data?.id; // Assuming proposalId is the same as data.id
                if (proposalId) {
                  const exportUrl = `/product-proposals/${proposalId}/pdf`;
                  handlePrint(exportUrl);
                } else {
                  toast.error("Missing AER ID.");
                }
              }}
            >
              <PiPrinter className="mt-1" />
              Print
            </Button>
          )}
        </div>
      </div>

      <div className="min-h-[50rem] w-full flex  justify-center gap-4   ">
        {/* COL2 */}
        <div>
          <div className="w-full flex flex-col items-center justify-center border border-zinc-200">
            <DisplayAERDetails proposalData={data} />
          </div>
        </div>
        {/* COL3 */}
        <div className="w-full items-center justify-center border border-zinc-200 p-4">
          <div className="h-1/2 w-full">
            <div className="w-full text-xl text-zinc-500  font-poppins-semibold py-4">APPROVAL STATUS</div>

            {canCreateProposal && data?.proposalApproval?.status !== ProposalApprovalStatus.approved && data?.proposalApproval?.status !== ProposalApprovalStatus.rejected && (
              <div className="w-full  text-zinc-500 text-center flex gap-4">
                <Button
                  type="button"
                  classNames="w-1/2 text-xs text-success bg-gradient-to-l  from-sky-50 to-white border border-zinc-200"
                  outline
                  onMouseEnter={() => setUpdateStatus("Approved")}
                  onClick={handleUpdateStatusModal}
                >
                  Approve
                </Button>
                <Button
                  type="button"
                  classNames="w-1/2 text-xs text-red-500 bg-gradient-to-l from-sky-50 to-white border border-zinc-200"
                  outline
                  onMouseEnter={() => setUpdateStatus("Reject")}
                  onClick={handleUpdateStatusModal}
                >
                  Reject
                </Button>
              </div>
            )}
            {!canCreateProposal && data?.proposalApproval?.status !== ProposalApprovalStatus.approved && data?.proposalApproval?.status !== ProposalApprovalStatus.rejected && (
              <div className="bg-amber-50 text-amber-400 p-4 text-xs mt-4 rounded-md">
                <strong className="font-poppins-semibold">PENDING</strong> - awaiting approval from the cooperative.
              </div>
            )}
            {data?.proposalApproval?.status !== ProposalApprovalStatus.forReview && (
              <div>
                <div className="w-full">
                  <div className="flex justify-between text-sm mb-4 border-b pb-4 border-zinc-300">
                    <div>Status</div>
                    <div>
                      {data?.proposalApproval?.status === ProposalApprovalStatus.approved
                        ? ProposalApprovalStatus.approved
                        : data?.proposalApproval?.status === ProposalApprovalStatus.rejected
                          ? ProposalApprovalStatus.rejected
                          : data?.proposalApproval?.status?.toUpperCase()}
                    </div>
                  </div>

                  <div className="flex justify-between text-sm mb-4 border-b pb-4 border-zinc-300">
                    <div>Date {data?.status === ProposalApprovalStatus.approved ? "Approved" : data?.status === ProposalApprovalStatus.rejected ? "Rejected" : ""}</div>
                    <div>
                      {" "}
                      <span>{dayjs(formik?.values?.approveRejectDate).format("MMMM DD, YYYY") ?? ""}</span>
                    </div>
                  </div>

                  <div className="flex flex-col gap-4 mb-4  text-sm">
                    <div>Attachment</div>

                    <div className="underline text-accent hover:cursor-pointer">
                      <a
                        href={
                          (data?.proposalApproval?.attachments?.[0] as any)?.filepath ? `${import.meta.env.VITE_AWS_S3_ENDPOINT}/${(data.proposalApproval?.attachments?.[0] as any)?.filepath}` : "#"
                        }
                        target="_blank"
                        rel="noopener noreferrer" // This is important for security reasons
                      >
                        {data?.proposalApproval?.attachments[0]?.label ? data?.proposalApproval?.attachments[0]?.label : formik.values?.attachments[0]?.name}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductProposalAER;
