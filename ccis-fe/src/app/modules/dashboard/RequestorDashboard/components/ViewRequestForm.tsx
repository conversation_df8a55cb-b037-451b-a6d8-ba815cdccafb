import Button from "@components/common/Button";
import Typography from "@components/common/Typography";
import { useEffect, useState, FC, ReactNode, useRef } from "react";
import TicketDetails from "./TicketDetails";
import Approvals from "./Approvals";
import TicketLogs from "./TicketLogs";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import AttachmentItem from "@modules/admin/form-inventory-and-tracking/new-forms/incoming/components/attachmentItem";
import { FaPaperclip, FaPaperPlane, FaTimes } from "react-icons/fa";
import { RiArrowGoBackLine } from "react-icons/ri";
import { RootState } from "@state/store";
import { useSelector } from "react-redux";
import Loader from "@components/Loader";
import { imageStorageUrl } from "@services/variables";
import ForwardTicketModal from "./modals/ForwardTicketModal";
import { RequestStatus } from "@enums/ticket-status";
import { ITicketAssigneeRoleConstant } from "@interface/ticket-assignee-roles.interface";
import { useTicketActions } from "@state/reducer/departmental-ticketing";
import { AttachmentTags } from "@enums/attachment-tags";
import { toast } from "react-toastify";
import { IAttachments } from "@interface/common.interface";
import { ITicketInfo } from "@interface/departmental-ticketing-interface";
import { getRolePath } from "@helpers/navigatorHelper";
import useIsMobile from "@hooks/useIsMobile";
import { downloadAttachmentService } from "@services/shared/shared.service";
import VPSApprovalModal from "./modals/VPSApprovalModal";
import { SharedRoutes } from "@enums/shared-routes";
import Tabs from "./common/Tabs";

const S3_PREFIX = imageStorageUrl + "/";

const ViewRequestForm: FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [ticket, setTicket] = useState<any>(null);
  const user = useSelector((state: RootState) => state?.auth?.user?.data);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const headers = ["Details", "Approvals", "Logs"].map((header) => <span>{header}</span>);
  const managerIds = useSelector((state: RootState) => state?.globalSettings?.getGlobalSettings?.data?.data?.find((setting: any) => setting.key === "managers_user_ids")?.value);
  const [isForwardTicketModalOpen, setIsForwardTicketModalOpen] = useState<boolean>(false);

  // File attachment state
  const [files, setFiles] = useState<File[]>([]);
  const [attachments, setAttachments] = useState<IAttachments[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [commentText, setCommentText] = useState<string>("");

  const { getTicketAssigneeDetails, clearSelectedTicket, postTicketComment, postTicketCommentAttachment, clearTicketAssigneeDetails } = useTicketActions();

  // Redux states
  const putTicketExtendDateState = useSelector((state: RootState) => state?.departmentalTicketing?.putTicketExtendDate);
  // const putUpdateTicketStatusState = useSelector((state: RootState) => state?.departmentalTicketing?.putUpdateTicketStatus);
  const postUpdateTicketApprovalState = useSelector((state: RootState) => state?.departmentalTicketing?.postUpdateTicketApproval);
  const ticketState = useSelector((state: RootState) => state?.departmentalTicketing?.getTicketAssigneeDetails?.data);
  const postCommentState = useSelector((state: RootState) => state?.departmentalTicketing?.postTicketComment);
  const postAttachmentState = useSelector((state: RootState) => state?.departmentalTicketing?.postTicketCommentAttachment);
  const isMobile = useIsMobile();
  const ticketStateLoading = useSelector((state: RootState) => state?.departmentalTicketing?.getTicketAssigneeDetails?.loading);
  const prevPostUpdateSuccessRef = useRef(false);
  // const prevPutUpdateSuccessRef = useRef(false);
  const prevExtendDateSuccessRef = useRef(false);
  const prevAssignTicketUserSuccessRef = useRef(false);
  const prevAssignTicketUserSuccessState = useSelector((state: RootState) => state?.departmentalTicketing?.postAssignTicketUser);
  const [vpsApprovalModalOpen, setVpsApprovalModalOpen] = useState<boolean>(false);
  const postForwardTicketState = useSelector((state: RootState) => state?.departmentalTicketing?.postTicketForward);
  const prevPostForwardTicketSuccessRef = useRef(false);
  const selectedTicketId = useParams();
  // Only depends on loading states
  useEffect(() => {
    let shouldRefresh = false;

    // Check if postUpdateTicketApproval just succeeded
    if (postUpdateTicketApprovalState?.success && !prevPostUpdateSuccessRef.current) {
      shouldRefresh = true;
    }

    // // Check if putUpdateTicketStatus just succeeded
    // if (putUpdateTicketStatusState?.success && !prevPutUpdateSuccessRef.current) {
    //   shouldRefresh = true;
    // }

    // Check if putTicketExtendDate just succeeded
    if (putTicketExtendDateState?.success && !prevExtendDateSuccessRef.current) {
      shouldRefresh = true;
    }

    if (prevAssignTicketUserSuccessState?.success && !prevAssignTicketUserSuccessRef.current) {
      shouldRefresh = true;
    }

    if (postForwardTicketState?.success && !prevPostForwardTicketSuccessRef.current) {
      shouldRefresh = true;
    }

    if (shouldRefresh) {
      handleFetchTicketByID();
    }

    // Update both refs
    prevExtendDateSuccessRef.current = putTicketExtendDateState?.success || false;
    prevAssignTicketUserSuccessRef.current = prevAssignTicketUserSuccessState?.success || false;
    prevPostUpdateSuccessRef.current = postUpdateTicketApprovalState?.success || false;
    prevPostForwardTicketSuccessRef.current = postForwardTicketState?.success || false;
  }, [putTicketExtendDateState?.success, prevAssignTicketUserSuccessState?.success, postUpdateTicketApprovalState?.success, postForwardTicketState?.success]);

  const handleFetchTicketByID = async () => {
    setIsLoading(true);
    const id = location.pathname.split("/").pop();
    if (!id || isNaN(parseInt(id, 10))) {
      return;
    }

    await getTicketAssigneeDetails({
      params: {
        id,
        relations: "ticketAssignees.user|toDepartment|fromDepartment|assignedTo|requestType|ticketAssignees|attachments|createdBy.position|ticketComments.user",
      },
    });
    setIsLoading(false);
  };

  useEffect(() => {
    if (user) {
      handleFetchTicketByID();
    }
  }, [user, selectedTicketId]);

  const handleSetFiles = (newFiles: File[]) => {
    setFiles([...files, ...newFiles]);
    const newAttachments: IAttachments[] = newFiles.map((file) => ({
      file,
      label: file.name,
      size: file.size,
    }));
    setAttachments([...attachments, ...newAttachments]);
  };

  const handleRemoveFile = (index: number) => {
    const updatedAttachments = attachments.filter((_, i) => i !== index);
    const updatedFiles = files.filter((_, i) => i !== index);
    setAttachments(updatedAttachments);
    setFiles(updatedFiles);
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    const allowedTypes = ["application/pdf", "image/png", "image/jpeg"];
    const maxFiles = 5;

    // Filter valid files
    const validFiles = selectedFiles.filter((file) => {
      const isValidType = allowedTypes.includes(file.type);
      if (!isValidType) {
        toast.error(`${file.name} is not a valid file type. Only PDF, PNG, and JPEG files are allowed.`);
      }
      return isValidType;
    });

    // Check file limit
    if (files.length + validFiles.length > maxFiles) {
      toast.error(`You can only upload up to ${maxFiles} files.`);
      return;
    }

    if (validFiles.length > 0) {
      handleSetFiles(validFiles);
    }
  };

  const handleAttachmentClick = () => {
    fileInputRef.current?.click();
  };

  const handleSubmitComment = async () => {
    if (isSubmitting) {
      return;
    }
    if (!commentText.trim() && attachments.length === 0) {
      toast.error("Please enter a comment or attach files.");
      return;
    }

    // Submit the comment first if there's text
    if (commentText.trim()) {
      postTicketComment({
        ticketId: ticket?.id,
        comment: commentText.trim(),
      });
    }

    // Handle attachments if they exist using Redux action
    if (attachments.length > 0 && ticket?.id) {
      postTicketCommentAttachment({
        attachableType: AttachmentTags.TICKET,
        attachableId: ticket.id,
        files: files,
      });
    }

    // Don't clear state here - let useEffect handle it after success
  };

  const formatFileSize = (bytes: number | undefined) => {
    if (bytes === undefined) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const contents: ReactNode[] = [
    <TicketDetails
      ticketId={ticket?.id ? parseInt(ticket.id, 10) : 0}
      priorityLevel={ticket?.priorityLevel}
      closureStatus={ticket?.closureStatus}
      status={ticket?.status}
      createdAt={ticket?.createdAt}
      expectedCompletionDate={ticket?.expectedCompletionDate}
      toDepartmentName={ticket?.toDepartment?.departmentName}
      assignedToFirstname={ticket?.assignedTo?.firstname}
      assignedToLastname={ticket?.assignedTo?.lastname}
      extensionDate={ticket?.extensionDate}
      requestTypeName={ticket?.requestType?.name}
      createdBy={ticket?.createdBy?.id}
      assignedToId={ticket?.assignedToId}
      handleFetchTicketByID={handleFetchTicketByID}
      managerIds={managerIds}
      toDepartmentId={ticket?.toDepartment?.id}
      ticketAssignees={ticket?.ticketAssignees}
      requestingDepartment={ticket?.fromDepartment?.departmentName}
      userTicketCreatorFirstName={ticket?.createdBy?.firstname}
      userTicketCreatorLastName={ticket?.createdBy?.lastname}
    />,
    <Approvals ticket={ticket} />,
    <TicketLogs ticket={ticket} />,
  ];

  useEffect(() => {
    if (ticketState) {
      setTicket(ticketState[0]);

      setIsLoading(false);
    }
  }, [ticketState]);

  // Handle postTicketComment state changes
  useEffect(() => {
    if (postCommentState?.success && !postCommentState?.loading) {
      // Only clear comment text if there are no attachments to process
      if (attachments.length === 0) {
        setCommentText("");
        handleFetchTicketByID();
      }
    } else if (postCommentState?.error && !postCommentState?.loading) {
      toast.error("Failed to submit comment. Please try again.");
    }
  }, [postCommentState]);

  // Handle postTicketCommentAttachment state changes
  useEffect(() => {
    if (postAttachmentState?.success && !postAttachmentState?.loading) {
      // Clear all states after successful attachment upload
      setCommentText("");
      setFiles([]);
      setAttachments([]);

      // Refresh ticket data to show new attachments
      handleFetchTicketByID();
    } else if (postAttachmentState?.error && !postAttachmentState?.loading) {
      toast.error("Failed to save attachments. Please try again.");
    }
  }, [postAttachmentState]);

  // const isUserReceivingDepartment = () => {
  //   if (!user?.id) return false;

  //   return ticket?.ticketAssignees?.some((assignee: any) => assignee.role === ITicketAssigneeRoleConstant.receivingDepartment && assignee.userId === user.id);
  // };

  const goBacktoDashboard = () => {
    setTicket([]);
    clearTicketAssigneeDetails();
    navigate(getRolePath(SharedRoutes.REQUEST_DASHBOARD));
    clearSelectedTicket();
  };

  // Check if any operation is loading
  const isSubmitting = postCommentState?.loading || postAttachmentState?.loading;

  function findForwardReceivingDepartment(currentUserId: number) {
    return ticket?.ticketAssignees.find((assignee: any) => assignee.userId === currentUserId && assignee.role === ITicketAssigneeRoleConstant.receivingDepartment);
  }

  const isUserTicketAssignee = (ticket: ITicketInfo, userId: number) => {
    if (!ticket?.ticketAssignees || !userId) {
      return false;
    }

    return ticket.ticketAssignees.some((assignee) => assignee?.role === ITicketAssigneeRoleConstant.ticketAssignee && assignee?.user?.id === userId);
  };

  const isUserAssigned = (ticket: ITicketInfo) => {
    if (!user?.id || !ticket?.ticketAssignees) return false;
    return ticket.ticketAssignees.some((assignee) => assignee?.userId === user.id);
  };

  // / Download handler
  const handleDownload = async (attachmentId: number) => {
    try {
      const response = await downloadAttachmentService(attachmentId);
      const fileUrl = response.data || response; // Adjust based on your API response structure

      const attachment = ticket?.attachments?.find((att: any) => att.id === attachmentId);
      const filename = attachment?.filename || `attachment_${attachmentId}`;

      // Try to fetch and download as blob first
      try {
        const fileResponse = await fetch(fileUrl);
        const blob = await fileResponse.blob();

        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = filename;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (fetchError) {
        // Fallback to opening in new tab if fetch fails
        window.open(fileUrl, "_blank");
      }
    } catch (error) {
      toast.error("Failed to download attachment. Please try again.");
    }
  };

  if ((isLoading || ticketStateLoading) && !postCommentState?.success) {
    return (
      <div className="flex flex-1 w-full items-center justify-center">
        <Loader />
      </div>
    );
  }
  return (
    <div>
      <Button classNames="text-white flex items-center justify-center border-primary border rounded-xl hover:bg-zinc-200" onClick={goBacktoDashboard}>
        <RiArrowGoBackLine className="text-primary w-6 h-8" />
      </Button>
      <div className={`flex gap-4 p-6 ${isMobile ? "flex-col" : ""}`}>
        <div className="w-full h-full">
          <Typography className="font-poppins-semibold mt-4 pl-2 mb-2" variant="primary" size="2xl">
            Request Details
          </Typography>

          <div className="mb-6">
            <Typography className="text-base text-slate-600 font-poppins-semibold border-b border-slate-300 p-2">Request Type</Typography>
            <Typography className="text-slate-700 text-md p-2">{ticket?.requestType?.name || "N/A"}</Typography>
          </div>
          <div className="mb-6">
            <Typography className="text-base text-slate-600 font-poppins-semibold border-b border-slate-300 p-2">Description</Typography>
            <Typography className="text-slate-700 text-md p-2">{ticket?.description || "No description available"}</Typography>
          </div>
          <div className="mb-8">
            <Typography className="text-base text-slate-600 font-poppins-semibold border-b border-slate-300 p-2">Attachment</Typography>
            <div className="w-full flex flex-col gap-2 mt-2">
              {ticket?.attachments?.length > 0 ? (
                ticket.attachments.map((attachment: any, index: number) => (
                  <div className="flex items-center justify-between bg-slate-100 rounded-md px-2" key={index}>
                    <AttachmentItem
                      key={index}
                      fileName={attachment.filename || "Unknown File"}
                      filePath={
                        attachment.filepath
                          ? `${S3_PREFIX}${attachment.filepath
                              .replace(/\\/g, "/")
                              .replace(/^\.\//, "")
                              .replace(/^Uploads\/attachments\//, "uploads/attachments/")}`
                          : ""
                      }
                    />
                    <Button variant="customWhite" outline onClick={() => handleDownload(attachment.id)}>
                      Preview
                    </Button>
                  </div>
                ))
              ) : (
                <Typography className="text-base text-slate-500 p-2">No attachments available</Typography>
              )}
            </div>
          </div>
          <Typography className="text-base text-slate-600 font-poppins-semibold border-b border-slate-300 p-2">Comments</Typography>
          <div className={`relative ${isMobile ? "h-[400px]" : "h-[600px]"} overflow-y-scroll px-3`}>
            {ticket?.ticketComments?.length > 0 ? (
              ticket.ticketComments.map((comment: any, index: number) => (
                <div key={index} className="flex flex-col gap-2">
                  {comment.type === "LOG" ? (
                    <div className="w-full flex flex-col">
                      <div className={`divider flex ${isMobile ? "flex-col" : "flex-row"} text-sm items-center justify-center`}>
                        <p className="bg-gray-100 rounded text-slate-400">{comment.comment}</p>
                        <span className="text-slate-400">{new Date(comment.createdAt).toLocaleString()}</span>
                      </div>
                    </div>
                  ) : (
                    <div className="w-full items-center flex gap-2">
                      {comment.user.profilePicturePath ? (
                        <img src={`${S3_PREFIX}${comment.user.profilePicturePath.replace(/\\/g, "/")}`} alt="User Avatar" className="h-12 w-12 rounded-full" />
                      ) : (
                        <div className="h-12 w-12 p-4 rounded-full bg-[#94a3b8] flex items-center justify-center text-2xl text-gray-200">
                          {comment.user?.firstname?.[0] ?? ""}
                          {comment.user?.lastname?.[0] ?? ""}
                        </div>
                      )}
                      <div className="w-full ">
                        <div className="flex gap-4">
                          <p className="font-medium">{`${comment.user?.firstname || ""} ${comment.user?.lastname || ""}`.trim() || "Unknown User"}</p>
                          <p className="font-regular text-sm text-slate-400">{new Date(comment.createdAt).toLocaleString() || "N/A"}</p>
                        </div>
                        <p className="font-medium border border-slate-200 p-2 bg-slate-100 rounded">{comment.comment || "No comment"}</p>
                      </div>
                    </div>
                  )}
                </div>
              ))
            ) : (
              <Typography className="text-slate-600 p-2">No comments available</Typography>
            )}

            {/* Show attached files preview */}
            {attachments.length > 0 && (
              <div className="p-2 border border-slate-200 rounded mb-2">
                <Typography className="text-sm font-semibold mb-2">Attached Files:</Typography>
                <div className="space-y-2">
                  {attachments.map((attachment, index) => (
                    <div key={index} className="flex items-center justify-between bg-slate-50 p-2 rounded">
                      <div className="flex items-center gap-2">
                        <FaPaperclip className="text-slate-500" />
                        <span className="text-sm">{attachment.label}</span>
                        <span className="text-xs text-slate-500">({formatFileSize(attachment.size)})</span>
                      </div>
                      <Button classNames="bg-red-500 hover:bg-red-600 text-red-600" onClick={() => handleRemoveFile(index)} disabled={isSubmitting}>
                        <FaTimes />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="sticky bottom-0 bg-white mt-2">
              <div className="w-full flex gap-2">
                {user?.profilePicturePath ? (
                  <img src={`${S3_PREFIX}${user.profilePicturePath.replace(/\\/g, "/")}`} alt="User Avatar" className="h-12 w-12 rounded-full" />
                ) : (
                  <div className="h-12 w-12 p-4 rounded-full bg-[#94a3b8] flex items-center justify-center text-2xl text-gray-200">
                    {user?.firstname?.[0] ?? ""}
                    {user?.lastname?.[0] ?? ""}
                  </div>
                )}
                <div className="flex gap-2 w-full border border-slate-400 p-1 rounded">
                  <input
                    placeholder="Add a Comment"
                    className="w-full rounded py-2 px-2"
                    value={commentText}
                    onChange={(e) => setCommentText(e.target.value)}
                    disabled={isSubmitting || !isUserAssigned(ticket)}
                  />
                  <input type="file" ref={fileInputRef} onChange={handleFileInputChange} multiple accept=".pdf,.png,.jpeg,.jpg" className="hidden" />
                  <Button classNames="btn-sm bg-slate-100 hover:bg-slate-200 mt-1" onClick={handleAttachmentClick} disabled={isSubmitting || !isUserAssigned(ticket)}>
                    <FaPaperclip className="text-slate-700" />
                  </Button>
                  <Button classNames="text-white bg-sky-600 btn-sm mt-1 hover:bg-sky-700" onClick={handleSubmitComment} disabled={isSubmitting || !isUserAssigned(ticket)}>
                    {isSubmitting ? <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div> : <FaPaperPlane className="text-white" />}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className={`border border-slate-300 pt-4 sticky top-4 self-start overflow-y-scroll h-full ${isMobile ? "w-full" : "w-1/2"}`}>
          <div className="flex justify-center mb-2">
            {ticket?.createdBy?.profilePicturePath ? (
              <img src={`${S3_PREFIX}${ticket.createdBy.profilePicturePath.replace(/\\/g, "/")}`} alt="User Avatar" className="h-40 w-40 rounded-full" />
            ) : (
              <div className="z-40 h-40 w-40 p-4 rounded-full bg-[#94a3b8] flex items-center justify-center text-2xl font-bold text-gray-200">
                {ticket?.createdBy?.firstname?.[0] ?? ""}
                {ticket?.createdBy?.lastname?.[0] ?? ""}
              </div>
            )}
          </div>
          <Typography className="text-lg text-center text-slate-600 font-poppins-semibold">
            {`${ticket?.createdBy?.firstname || ""} ${ticket?.createdBy?.middlename || ""} ${ticket?.createdBy?.lastname || ""}`.trim() || "N/A"}
          </Typography>
          <p className="text-center">{ticket?.createdBy?.email || "N/A"}</p>
          <p className="text-center font-poppins-semibold">Ticket # {ticket?.id || "N/A"}</p>
          <div className="px-4 my-4">
            <Tabs headers={headers} contents={contents} headerClass="border border-primary font-poppins-semibold text-sm" activeTabClassName="bg-primary text-white" />
          </div>
        </div>
      </div>

      <div className="absolute bottom-5">
        {!(ticket?.status === RequestStatus.InProgress || ticket?.status === RequestStatus.Resolved || ticket?.status === RequestStatus.Unresolved) &&
          (findForwardReceivingDepartment(user?.id) || isUserTicketAssignee(ticket, user?.id)) && (
            <div className="flex justify-end gap-2">
              <Button classNames="bg-primary hover:bg-primary-dark text-white text-xs w-full" onClick={() => setIsForwardTicketModalOpen(true)}>
                Forward Ticket
              </Button>
            </div>
          )}
      </div>

      <ForwardTicketModal ticketId={ticket?.id} isOpen={isForwardTicketModalOpen} onClose={() => setIsForwardTicketModalOpen(false)} handleFetchTicketByID={handleFetchTicketByID} />
      <VPSApprovalModal ticketId={ticket?.id} isOpen={vpsApprovalModalOpen} onClose={() => setVpsApprovalModalOpen(false)} handleFetchTicketByID={handleFetchTicketByID} />
    </div>
  );
};

export default ViewRequestForm;
