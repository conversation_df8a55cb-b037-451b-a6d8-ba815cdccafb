import { FormStatus } from "@enums/form-status";
import { createDateChangeHandler, createSelectChangeHandler } from "@helpers/handlers";
import { useDebouncedSearch } from "@hooks/useDebouncedSearch";
import { useFetchWithParams } from "@hooks/useFetchWithParams";
import { useSelectOptions } from "@hooks/useSelectOptions";
import TableFilter from "@modules/treasury/request-pads/table/filter/TableFilter";
import { RootState } from "@state/reducer";
import { getTransmittalTrailActivityLog, useTransmittalFormActions } from "@state/reducer/form-inventory-transmittal";
import { useDivisionActions } from "@state/reducer/form-inventory-utilities-divisions";
import { useFormTypeActions } from "@state/reducer/form-inventory-utilities-form-types";
import { useAreaActions } from "@state/reducer/utilities-areas";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { getColumns } from "../table/column/return-pads-column";
import Table from "@components/common/Table";
import Button from "@components/common/Button";
import Modal from "@components/common/Modal";
import Filter from "@components/common/Filter";
import Select from "@components/form/Select";
import SeriesTransmittalTable from "@modules/admin/form-inventory-and-tracking/used-forms/components/table/CreateTransmittalForm";
import { IActions, IDefaultParams } from "@interface/common.interface";
import {useNavigate} from "react-router-dom";
import { ROUTES } from "@constants/routes";
import { GoVersions } from "react-icons/go";

const ReturnPadTab = () => {
  // States
  const [resetCounter, setResetCounter] = useState(0);
  const [divisionFilter, setDivisionFilter] = useState<number>(0);
  const [type, setType] = useState<number>(0);
  const [area, setArea] = useState<number>(0);
  const [dateFrom, setDateFrom] = useState<string>("");
  const [dateTo, setDateTo] = useState<string>("");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isFormOpen, setIsFormOpen] = useState<boolean>(false);
  const navigate = useNavigate();

  // Global State

  // for Select options
  const divisions = useSelector((state: RootState) => state.formInventoryUtilitiesDivisions.divisions);
  const types = useSelector((state: RootState) => state.formInventoryUtilitiesFormTypes.formTypes);
  const areas = useSelector((state: RootState) => state.utilitiesAreas.areas);
  const returnPads = useSelector((state: RootState) => state.formInventoryTransmittal.getCurrentUserFormTransmittalTrail);
  const user = useSelector((state: RootState) => state.auth.user.data);
  const loading = useSelector((state: RootState) => state.formInventoryTransmittal.getCurrentUserFormTransmittalTrail.loading);

  // Actions
  const { getDivisions } = useDivisionActions();
  const { getFormTypes } = useFormTypeActions();
  const { getAreas } = useAreaActions();
  const { getCurrentUserFormTransmittalTrail } = useTransmittalFormActions();

  // Custom hooks
  const { value: searchText, handleChange: handleSearch, setValue: setSearchText } = useDebouncedSearch();

  const divisionOptions = useSelectOptions({
    data: divisions,
    firstOptionText: "Select Division",
    valueKey: "id",
    textKey: "divisionName",
  });

  const typeOptions = useSelectOptions({
    data: types,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "formTypeName",
  });

  const areaOptions = useSelectOptions({
    data: areas,
    firstOptionText: "Select Type",
    valueKey: "id",
    textKey: "areaName",
  });

  // Select handlers
  const handleDivisionChange = createSelectChangeHandler(setDivisionFilter);
  const handleTypeChange = createSelectChangeHandler(setType);
  const handleAreaChange = createSelectChangeHandler(setArea);

  // Date handlers
  const handleDateFromChange = createDateChangeHandler(setDateFrom);
  const handleDateToChange = createDateChangeHandler(setDateTo);

  const handleToggleFormModal = () => {
    setIsFormOpen((prev) => !prev);
  };

  const getActionEvents = (row: any): IActions<any>[] => {
      const actions: IActions<any>[] = [
        {
          name: "View",
          event: () => {
            getTransmittalTrailActivityLog({ id: row.id });
            navigate(ROUTES.INCOMINGOUTGOINGCASHIER.viewReturnedForm.parse(row.id));
          },
          icon: GoVersions,
          color: "primary",
        },
      ];
      return actions;
    };

  useFetchWithParams(
    [getDivisions, getFormTypes, getAreas],
    {
      filter: "",
    },
    [],
    false
  );

  const fetchForms = () => {
    const payload = {
      page,
      pageSize,
      filter: searchText,
      dateFrom: dateFrom,
      dateTo: dateTo,
      statusFilter: FormStatus.RETURNED,
      id: user.id,
      useReturnedPads: true,
    } as IDefaultParams;
    getCurrentUserFormTransmittalTrail(payload);
  };

  useEffect(() => {
    fetchForms();
  }, [searchText, divisionFilter, type, area, dateFrom, dateTo]);

  const columns = getColumns({ getActionEvents, divisions, areas, formTypes: types });

  const handleClearAll = () => {
    setSearchText("");
    setType(0);
    setDateFrom("");
    setDateTo("");
    setResetCounter((prev) => prev + 1);
  };

  return (
    <div className="p-4">
      <div className="mt-8">
        <div className="flex flex-row items-center justify-between">
          <TableFilter
            handleClearAll={handleClearAll}
            searchText={searchText}
            handleSearch={handleSearch}
            resetCounter={resetCounter}
            type={type}
            handleTypeChange={handleTypeChange}
            handleDivisionChange={handleDivisionChange}
            typeOptions={typeOptions}
            divisionFilter={divisionFilter}
            divisionOptions={divisionOptions}
            areaFilter={area}
            areaOptions={areaOptions}
            handleAreaChange={handleAreaChange}
            dateFrom={dateFrom}
            handleDateFromChange={handleDateFromChange}
            dateTo={dateTo}
            handleDateToChange={handleDateToChange}
          />
          <div className="flex justify-end gap-2">
            <Button classNames="text-primary text-xs btn-primary btn btn-sm" onClick={handleToggleFormModal}>
              + Add New
            </Button>
          </div>
        </div>

        <Table
          className="!min-h-[100%] h-[500px] border-[1px] border-zinc-300 mt-8"
          columns={columns}
          loading={loading}
          data={returnPads?.data?.data || []}
          searchable={false}
          multiSelect={false}
          paginationTotalRows={returnPads?.data?.data.length || [].length}
          paginationServer={true}
          onChangeRowsPerPage={(newPageSize, newPage) => {
            setPageSize(newPageSize);
            setPage(newPage);
          }}
          onPaginate={(newPage) => setPage(newPage)}
        />
        <Modal
          title={"Add New Transmittal"}
          modalContainerClassName="max-w-3xl mx-auto"
          titleClass="text-primary text-lg uppercase"
          isOpen={isFormOpen}
          onClose={handleToggleFormModal}
          className="w-full"
        >
          <div className="flex justify-start">
            <Filter search={searchText} onChange={handleSearch}>
              <div className="flex justify-end">
                <button className="text-primary text-xs btn-sm" type="button" onClick={handleClearAll}>
                  Clear All
                </button>
              </div>
              <div>
                <div className="text-xs">Division</div>
                <Select key={`division-${resetCounter}`} size="sm" options={divisionOptions} value={divisionFilter} onChange={handleDivisionChange} />
              </div>
            </Filter>
          </div>

          <SeriesTransmittalTable searchText={searchText} divisionFilter={divisionFilter} handleToggleFormModal={handleToggleFormModal} />
        </Modal>
      </div>
    </div>
  );
};

export default ReturnPadTab;
