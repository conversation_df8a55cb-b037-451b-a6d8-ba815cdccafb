import ActionDropdown from "@components/common/ActionDropdown";
import { Breadcrumbs } from "@components/common/Breadcrumbs";
import Table from "@components/common/Table";
import Typography from "@components/common/Typography";
import { ROUTES } from "@constants/routes";
import { ProposalStatus } from "@enums/proposal-status";
import { useUserId, useUserRoleName } from "@helpers/data";
import { formatDate } from "@helpers/date";
import { extractFirstPathSegment } from "@helpers/navigatorHelper";
import { capitalizeFirstLetterWords, formatStringAtoZ0to9, getTextStatusColor } from "@helpers/text";
import { IActions } from "@interface/common.interface";
import { IProductProposal } from "@interface/product-proposal.interface";
import { UserRoles } from "@interface/routes.interface";
import { useProductProposalActions } from "@state/reducer/product-proposal";
import { RootState } from "@state/store";
import React, { useEffect, useState } from "react";
import { TableColumn } from "react-data-table-component";
import { GoVersions } from "react-icons/go";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
const CommissionAndRequirements: React.FC = () => {
  const navigate = useNavigate();
  const [page, setPage] = useState<number>(1);
  const [rows, setRows] = useState<number>(10);
  const [pathBase, setPathBase] = useState<string>("");
  const { data: marketingProductProposals, loading: marketingProductProposalsLoading } = useSelector((state: RootState) => state.productProposal.getMarketingProductProposals);
  const { getMarketingProductProposals } = useProductProposalActions();

  const breadcrumbItems = [
    {
      label: capitalizeFirstLetterWords(extractFirstPathSegment(window.location.pathname, 0) || "", "-"),
      link: (ROUTES[pathBase as keyof typeof ROUTES] as any)?.marketingDashboard?.key || (ROUTES[pathBase as keyof typeof ROUTES] as any)?.dashboard?.key,
    },
    { label: pathBase === formatStringAtoZ0to9(UserRoles.marketing).toUpperCase() ? "Validation" : "My Approvals" },
    { label: "Commission and Requirements" },
  ];
  const userRole = useUserRoleName();
  const userId = useUserId();
  const filterSignatory = "&commissionStructure.approval.signatories.userId[eq]";
  const fetchMarketingProductProposals = async () => {
    getMarketingProductProposals({
      // filter: searchText,
      nameFilter: formatStringAtoZ0to9(UserRoles.marketing) !== formatStringAtoZ0to9(userRole) ? filterSignatory + "=" + userId : "",
      type: formatStringAtoZ0to9(userRole),
      page: page,
      pageSize: rows,
      // condition:
      //   // "proposalApproval.status[eq]=APPROVED&requirementsStatus[neq]=PENDING&commissionStatus[neq]=PENDING",
      //   "requirementsStatus[neq]=PENDING&commissionStatus[neq]=PENDING",
    });
  };
  useEffect(() => {
    const firstSegment = extractFirstPathSegment(window.location.pathname, 0);
    if (firstSegment) {
      setPathBase(formatStringAtoZ0to9(firstSegment).toUpperCase());
    }
  }, []);

  const commonSetting = {
    sortable: true,
    reorder: true,
  };

  const getActionEvents = (productProposal: IProductProposal): IActions<IProductProposal>[] => {
    const actions: IActions<IProductProposal>[] = [
      ...(productProposal.status === ProposalStatus.active
        ? [
            {
              name: "View",
              event: (data: IProductProposal) => {
                let type = "standard";
                if (data.proposalType === "STANDARD" && data.proposableType === "PRODUCT_REVISION") {
                  type = "standard";
                } else if (data.proposalType === "CUSTOMIZED" && data.proposableType === "ACTUARY_EVALUATION_REPORT") {
                  type = "custom";
                }
                // Pick route based on user role and pathBase
                const route =
                  pathBase === formatStringAtoZ0to9(UserRoles.marketing).toUpperCase()
                    ? (ROUTES[pathBase as keyof typeof ROUTES] as any)?.viewProductProposal
                    : (ROUTES[pathBase as keyof typeof ROUTES] as any)?.viewProductProposalSignatory;

                navigate(route.parse(data.id), { state: { proposal: data, type } });
              },
              icon: GoVersions,
              color: "primary",
            },
          ]
        : []),
    ];

    return actions;
  };

  const handlePage = (pagination: number) => {
    setPage(pagination);
  };

  const handleRowsChange = (rowsPerPage: number, pagination: number) => {
    setRows(rowsPerPage);
    setPage(pagination);
  };

  const columns: TableColumn<IProductProposal>[] = [
    {
      name: "Product Name",
      cell: (row) => row?.product?.name ?? "Not Set",
      ...commonSetting,
    },
    {
      name: "Cooperative",
      cell: (row) => row.cooperative?.coopName ?? "Not Set",
      ...commonSetting,
    },
    {
      name: "Product Type",
      cell: (row) => row?.product?.productType?.productType ?? "",
      ...commonSetting,
    },
    {
      name: "Creation Date",
      cell: (row) => formatDate(row?.createdAt, "d MMMM yyyy"),
      ...commonSetting,
    },
    {
      name: "Status",
      cell: (row) => (
        <Typography size="xs" className={`${getTextStatusColor(row.status)}`}>
          {capitalizeFirstLetterWords(row.status, "_")}
        </Typography>
      ),
    },

    {
      name: <Typography className="flex flex-1 justify-center !text-black !text-xs">Actions</Typography>,
      cell: (row, rowIndex) => {
        return (
          <div className="flex flex-1 flex-row justify-center items-center gap-x-2">
            <ActionDropdown actions={getActionEvents(row)} data={row} rowIndex={rowIndex} />
          </div>
        );
      },
    },
  ];

  useEffect(() => {
    fetchMarketingProductProposals();
  }, [page, rows]);

  return (
    <div className="mt-20">
      <Breadcrumbs items={breadcrumbItems} />
      <h3 className="text-2xl font-bold mb-8">Commission and Requirements</h3>
      <Table
        className="h-[400px]"
        columns={columns}
        data={marketingProductProposals?.data}
        loading={marketingProductProposalsLoading}
        searchable={false}
        multiSelect={false}
        paginationTotalRows={marketingProductProposals?.meta?.total}
        paginationServer={true}
        onPaginate={handlePage}
        onChangeRowsPerPage={handleRowsChange}
      />
    </div>
  );
};

export default CommissionAndRequirements;
